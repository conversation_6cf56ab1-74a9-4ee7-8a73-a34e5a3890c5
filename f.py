import pandas as pd

# Charger la feuille ETA
file_path = "Application Sheet.xlsx"
df_raw = pd.read_excel(file_path, sheet_name="ETA", header=None)

# Trouver la première ligne avec des données d'entête
header_row = None
for i, row in df_raw.iterrows():
    if row.notna().sum() > 2:  # au moins 3 colonnes remplies
        header_row = i
        break

# Relire le fichier en utilisant cette ligne comme header
df = pd.read_excel(file_path, sheet_name="ETA", header=header_row)

# Nettoyage de base
df = df.dropna(how="all")  # supprimer lignes vides
df = df.dropna(axis=1, how="all")  # supprimer colonnes vides
df.columns = [str(c).strip() for c in df.columns]  # nettoyer noms de colonnes
df = df.reset_index(drop=True)

# Sauvegarder les données nettoyées
df.to_excel("ETA_Cleaned.xlsx", index=False)

print("Données ETA nettoyées et sauvegardées dans ETA_Cleaned.xlsx")
