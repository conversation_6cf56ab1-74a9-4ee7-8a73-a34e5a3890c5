#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script Python pour n8n
Lit Application Sheet.xlsx et génère 2 PDFs
Retourne les chemins des fichiers créés
"""

import os
import sys
import json
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
import numpy as np

# ============================================================================
# CONFIGURATION POUR N8N
# ============================================================================

# Chemin du fichier Excel (dans le même dossier que le script)
EXCEL_PATH = os.path.join(os.path.dirname(__file__), "Application Sheet.xlsx")

# Dossier de sortie pour les PDFs
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), "reports")
os.makedirs(OUTPUT_DIR, exist_ok=True)

# ============================================================================
# FONCTIONS DE GÉNÉRATION
# ============================================================================

def extract_excel_data():
    """Extrait les données de l'Excel"""
    try:
        # Lire les feuilles CAL et ETA
        cal_data = pd.read_excel(EXCEL_PATH, sheet_name='CAL', header=None).values.tolist()
        eta_data = pd.read_excel(EXCEL_PATH, sheet_name='ETA', header=None).values.tolist()
        
        # Extraction des données (simplifiée)
        client_nom = "Client Test"
        instrument_type = "Manomètre"
        instrument_serie = "12345"
        
        # Chercher les vraies valeurs
        for row in cal_data:
            if len(row) > 1:
                if str(row[0]).strip() == 'DELIVRE A :':
                    client_nom = str(row[1]).strip()
                elif str(row[0]).strip() == 'INSTRUMENT ETALONNE :':
                    instrument_type = str(row[1]).strip()
        
        for row in eta_data:
            if len(row) > 1 and str(row[0]).strip() == 'N° de série':
                instrument_serie = str(row[1]).strip()
        
        return {
            "client_nom": client_nom,
            "instrument_type": instrument_type,
            "instrument_serie": instrument_serie,
            "date_etalonnage": datetime.now().strftime('%d/%m/%Y')
        }
    except Exception as e:
        return {"error": str(e)}

def generate_pdf(filename, title, data):
    """Génère un PDF simple"""
    doc = SimpleDocTemplate(filename, pagesize=A4)
    styles = getSampleStyleSheet()
    
    story = []
    story.append(Paragraph(title, styles['Title']))
    story.append(Spacer(1, 12))
    
    # Tableau d'informations
    info_data = [
        ['Client', data['client_nom']],
        ['Instrument', data['instrument_type']],
        ['Série', data['instrument_serie']],
        ['Date', data['date_etalonnage']]
    ]
    
    table = Table(info_data, colWidths=[3*72, 3*72])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    story.append(table)
    doc.build(story)

# ============================================================================
# MAIN
# ============================================================================

def main():
    print("🎯 Traitement du fichier Excel...")
    
    # Vérifier que le fichier existe
    if not os.path.exists(EXCEL_PATH):
        print(json.dumps({
            "error": f"Fichier non trouvé : {EXCEL_PATH}",
            "files": []
        }))
        sys.exit(1)
    
    # Extraire les données
    data = extract_excel_data()
    
    if "error" in data:
        print(json.dumps({
            "error": data["error"],
            "files": []
        }))
        sys.exit(1)
    
    # Générer les PDFs
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    cv_path = os.path.join(OUTPUT_DIR, f"CV_{timestamp}.pdf")
    ce_path = os.path.join(OUTPUT_DIR, f"CE_{timestamp}.pdf")
    
    generate_pdf(cv_path, "CERTIFICAT DE VÉRIFICATION", data)
    generate_pdf(ce_path, "CERTIFICAT D'ÉTALONNAGE", data)
    
    # Vérifier que les fichiers ont été créés
    files_to_send = []
    for file_path in [cv_path, ce_path]:
        if os.path.exists(file_path):
            files_to_send.append(file_path)
    
    # Retourner les chemins pour n8n
    result = {
        "status": "success",
        "client_nom": data["client_nom"],
        "instrument_type": data["instrument_type"],
        "files": files_to_send,
        "cv_path": cv_path,
        "ce_path": ce_path,
        "output_dir": OUTPUT_DIR
    }
    
    print(json.dumps(result))

if __name__ == "__main__":
    main()