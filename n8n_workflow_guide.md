# 🚀 Guide d'utilisation du code JavaScript dans n8n

## 📋 Prérequis

Installez les dépendances Node.js nécessaires :

```bash
npm install xlsx pdfkit canvas chart.js
```

## 🔧 Configuration du Workflow n8n

### 1. **Nœud Trigger** (Déclencheur)
- Type : `Schedule Trigger` ou `Manual Trigger`
- Configuration : Selon vos besoins

### 2. **Nœud Function** (Code JavaScript principal)

Copiez ce code dans un nœud Function :

```javascript
// Importer le module d'extraction
const { generateCertificates } = require('./extraction_n8n.js');

// Exécuter la génération des certificats
try {
    console.log('🚀 Démarrage de la génération des certificats...');
    
    const result = generateCertificates();
    
    if (result.success) {
        console.log('✅ Certificats générés avec succès !');
        
        // Préparer les données pour les nœuds suivants
        return [{
            json: {
                success: true,
                files: result.files,
                results: {
                    client: result.results.client_nom,
                    instrument: result.results.instrument_type,
                    conforme: result.results.conforme,
                    erreur_max: result.results.erreur_max,
                    date: result.results.date_etalonnage
                },
                timestamp: result.timestamp,
                attachments: [
                    {
                        filename: `CV_${result.timestamp}.pdf`,
                        path: result.files.cv,
                        type: 'application/pdf'
                    },
                    {
                        filename: `CE_${result.timestamp}.pdf`,
                        path: result.files.ce,
                        type: 'application/pdf'
                    },
                    {
                        filename: `Graphique_${result.timestamp}.png`,
                        path: result.files.graph,
                        type: 'image/png'
                    }
                ]
            }
        }];
    } else {
        console.error('❌ Erreur lors de la génération:', result.error);
        
        return [{
            json: {
                success: false,
                error: result.error,
                timestamp: result.timestamp
            }
        }];
    }
    
} catch (error) {
    console.error('❌ Erreur critique:', error.message);
    
    return [{
        json: {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        }
    }];
}
```

### 3. **Nœud IF** (Vérification du succès)

- **Condition** : `{{ $json.success }} === true`
- **True** : Continuer vers l'envoi d'email
- **False** : Aller vers la gestion d'erreur

### 4. **Nœud Function** (Préparation des pièces jointes)

```javascript
// Lire les fichiers générés et les préparer pour l'email
const fs = require('fs');

const attachments = [];

$json.attachments.forEach(attachment => {
    if (fs.existsSync(attachment.path)) {
        const content = fs.readFileSync(attachment.path);
        
        attachments.push({
            filename: attachment.filename,
            content: content.toString('base64'),
            encoding: 'base64',
            contentType: attachment.type
        });
        
        console.log(`📎 Pièce jointe préparée: ${attachment.filename}`);
    } else {
        console.warn(`⚠️ Fichier non trouvé: ${attachment.path}`);
    }
});

return [{
    json: {
        ...$json,
        emailAttachments: attachments,
        emailSubject: `Certificats d'étalonnage - ${$json.results.client} - ${$json.results.date}`,
        emailBody: `
Bonjour,

Veuillez trouver ci-joint les certificats d'étalonnage pour :

📋 Client : ${$json.results.client}
🔧 Instrument : ${$json.results.instrument}
📅 Date d'étalonnage : ${$json.results.date}
✅ Conformité : ${$json.results.conforme ? 'CONFORME' : 'NON-CONFORME'}
📊 Erreur maximale : ${$json.results.erreur_max.toFixed(3)}

Documents joints :
- Certificat de Vérification (CV)
- Certificat d'Étalonnage (CE)
- Graphique d'étalonnage

Cordialement,
Laboratoire de Métrologie
        `
    }
}];
```

### 5. **Nœud Send Email** (Envoi par email)

Configuration :
- **To** : `<EMAIL>` (ou dynamique)
- **Subject** : `{{ $json.emailSubject }}`
- **Text** : `{{ $json.emailBody }}`
- **Attachments** : `{{ $json.emailAttachments }}`

### 6. **Nœud Function** (Archivage - Optionnel)

```javascript
// Archiver les fichiers après envoi
const fs = require('fs');
const path = require('path');

const archiveDir = `./archives/${new Date().getFullYear()}/${new Date().getMonth() + 1}`;

// Créer le dossier d'archive
if (!fs.existsSync(archiveDir)) {
    fs.mkdirSync(archiveDir, { recursive: true });
}

// Déplacer les fichiers
$json.attachments.forEach(attachment => {
    if (fs.existsSync(attachment.path)) {
        const archivePath = path.join(archiveDir, attachment.filename);
        fs.renameSync(attachment.path, archivePath);
        console.log(`📁 Archivé: ${attachment.filename}`);
    }
});

return [{
    json: {
        ...$json,
        archived: true,
        archiveDir: archiveDir
    }
}];
```

## 🎯 Structure complète du Workflow

```
1. Manual Trigger
   ↓
2. Function (Génération des certificats)
   ↓
3. IF (Vérification succès)
   ├─ True → 4. Function (Préparation email)
   │         ↓
   │         5. Send Email
   │         ↓
   │         6. Function (Archivage)
   │
   └─ False → 7. Send Email (Notification d'erreur)
```

## 📝 Variables d'environnement recommandées

Dans les paramètres n8n, définissez :

```javascript
{
    "EXCEL_FILE_PATH": "C:/Users/<USER>/Desktop/Stagge/Application Sheet.xlsx",
    "RAPPORTS_DIR": "C:/Users/<USER>/Desktop/Stagge/rapports",
    "EMAIL_FROM": "<EMAIL>",
    "EMAIL_TO": "<EMAIL>",
    "ARCHIVE_DIR": "C:/Users/<USER>/Desktop/Stagge/archives"
}
```

## 🚀 Avantages de cette approche

1. **Code JavaScript natif** : Pas besoin d'exécuter Python
2. **Intégration parfaite** : Directement dans n8n
3. **Gestion d'erreurs** : Contrôle complet du flux
4. **Performance** : Plus rapide que l'exécution externe
5. **Flexibilité** : Facile à modifier et étendre

## 🔧 Dépannage

- Vérifiez que le fichier Excel existe
- Assurez-vous que les dépendances npm sont installées
- Vérifiez les permissions d'écriture dans le dossier rapports
- Consultez les logs n8n pour les erreurs détaillées
