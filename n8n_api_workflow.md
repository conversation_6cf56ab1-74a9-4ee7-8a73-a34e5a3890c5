# 🚀 Workflow n8n avec API HTTP

## 📋 Architecture du workflow

```
Read Binary File (Excel) → HTTP Request (API) → Process Response → Send Email
```

## 🔧 Configuration des nœuds n8n

### 1. **Nœud "Read Binary File"** (Lecture du fichier Excel)

- **File Path** : `C:\Users\<USER>\Desktop\Stagge\Application Sheet.xlsx`
- **Property Name** : `excelFile`
- **As Binary Property** : ✅ Activé

### 2. **Nœud "HTTP Request"** (Appel de l'API)

**Configuration :**
- **Method** : `POST`
- **URL** : `http://localhost:5000/generate-certificates`
- **Body Content Type** : `JSON`

**Body (dans l'onglet Body) :**
```json
{
  "excel_base64": "{{ $binary.excelFile.data }}"
}
```

**Headers :**
```json
{
  "Content-Type": "application/json"
}
```

### 3. **Nœud "Function"** (Traitement de la réponse)

```javascript
// Traiter la réponse de l'API
const response = $json;

if (!response.success) {
    throw new Error(`Erreur API: ${response.error}`);
}

console.log('✅ Certificats générés avec succès !');
console.log(`📋 Client: ${response.data.client_nom}`);
console.log(`🔧 Instrument: ${response.data.instrument_type}`);
console.log(`✅ Conformité: ${response.data.conforme ? 'CONFORME' : 'NON-CONFORME'}`);

// Préparer les pièces jointes pour l'email
const attachments = [];

Object.keys(response.files).forEach(key => {
    const file = response.files[key];
    attachments.push({
        filename: file.filename,
        content: file.content,
        encoding: 'base64',
        contentType: file.type
    });
});

// Préparer l'email
const emailSubject = `Certificats d'étalonnage - ${response.data.client_nom} - ${response.data.date_etalonnage}`;
const emailBody = `
Bonjour,

Veuillez trouver ci-joint les certificats d'étalonnage :

📋 INFORMATIONS :
• Client : ${response.data.client_nom}
• Instrument : ${response.data.instrument_type}
• Constructeur : ${response.data.instrument_constructeur}
• N° de série : ${response.data.instrument_serie}
• Date d'étalonnage : ${response.data.date_etalonnage}

📊 RÉSULTATS :
• Conformité : ${response.data.conforme ? '✅ CONFORME' : '❌ NON-CONFORME'}
• Erreur maximale : ${response.data.erreur_max.toFixed(3)} ${response.data.instrument_unite}
• Hystérésis maximale : ${response.data.hysteresis_max.toFixed(3)} ${response.data.instrument_unite}
• Tolérance : ±${response.data.tolerance} ${response.data.instrument_unite}

📎 DOCUMENTS JOINTS :
• Certificat de Vérification (CV)
• Certificat d'Étalonnage (CE)
• Graphique d'étalonnage

Cordialement,
Laboratoire de Métrologie
`;

return [{
    json: {
        success: true,
        emailSubject: emailSubject,
        emailBody: emailBody,
        attachments: attachments,
        results: response.data,
        timestamp: response.timestamp
    }
}];
```

### 4. **Nœud "Send Email"** (Envoi par email)

**Configuration :**
- **To** : `<EMAIL>` (ou dynamique avec `{{ $json.results.client_email }}`)
- **Subject** : `{{ $json.emailSubject }}`
- **Text** : `{{ $json.emailBody }}`
- **Attachments** : `{{ $json.attachments }}`

## 🚀 Démarrage du serveur API

### 1. **Installation des dépendances :**

```bash
pip install flask pandas matplotlib reportlab openpyxl
```

### 2. **Démarrage du serveur :**

```bash
python api_extraction_server.py
```

Le serveur démarre sur `http://localhost:5000`

### 3. **Test de l'API :**

```bash
curl http://localhost:5000/health
```

## 📡 Endpoints disponibles

### **GET /health**
- **Description** : Vérification de l'état du serveur
- **Réponse** : `{"status": "healthy", "timestamp": "..."}`

### **POST /generate-certificates**
- **Description** : Génération des certificats
- **Body** : 
  ```json
  {
    "excel_base64": "base64_encoded_excel_file"
  }
  ```
- **Réponse** :
  ```json
  {
    "success": true,
    "data": { /* données extraites */ },
    "files": {
      "cv": {
        "filename": "CV_20250811_143022.pdf",
        "content": "base64_pdf_content",
        "type": "application/pdf"
      },
      "ce": { /* ... */ },
      "graphique": { /* ... */ }
    },
    "timestamp": "2025-08-11T14:30:22"
  }
  ```

## 🎯 Avantages de cette approche

1. **API indépendante** : Serveur séparé, pas de modification d'`extraction.py`
2. **Intégration n8n native** : Utilise les nœuds HTTP Request standard
3. **Gestion d'erreurs** : Réponses HTTP structurées
4. **Flexibilité** : Peut être utilisé par d'autres applications
5. **Scalabilité** : Peut traiter plusieurs demandes simultanées

## 🔧 Workflow n8n complet

```
1. Manual Trigger (ou Schedule)
   ↓
2. Read Binary File (Excel)
   ↓
3. HTTP Request (API /generate-certificates)
   ↓
4. Function (Process Response)
   ↓
5. Send Email (Avec pièces jointes)
   ↓
6. Set (Log des résultats)
```

## 🛠️ Dépannage

- **Erreur connexion** : Vérifiez que le serveur API est démarré
- **Erreur fichier** : Vérifiez le chemin du fichier Excel
- **Erreur génération** : Consultez les logs du serveur API
- **Erreur email** : Vérifiez la configuration SMTP de n8n

Cette solution vous permet d'utiliser votre logique d'extraction via une API HTTP propre, sans modifier `extraction.py` !
