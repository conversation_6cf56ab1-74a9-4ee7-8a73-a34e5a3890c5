@echo off
echo 🚀 Démarrage du serveur API d'extraction...
echo.

cd /d "C:\Users\<USER>\Desktop\Stagge"

echo 📦 Vérification des dépendances Python...
pip install flask pandas matplotlib reportlab openpyxl

echo.
echo 🌐 Démarrage du serveur sur http://localhost:5000
echo 📡 Endpoints disponibles:
echo    - GET  /health
echo    - POST /generate-certificates
echo.
echo ⚠️  Pour arrêter le serveur, appuyez sur Ctrl+C
echo.

python api_extraction_server.py

pause
