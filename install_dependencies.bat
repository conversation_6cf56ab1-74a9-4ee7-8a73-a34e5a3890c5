@echo off
echo 📦 Installation des dépendances pour l'API d'extraction...
echo.

cd /d "C:\Users\<USER>\Desktop\Stagge"

echo 🔧 Installation des packages Python...
pip install --upgrade pip
pip install flask
pip install pandas
pip install matplotlib
pip install reportlab
pip install openpyxl
pip install xlrd

echo.
echo ✅ Installation terminée !
echo.
echo 🚀 Vous pouvez maintenant démarrer le serveur avec:
echo    python api_extraction_server.py
echo.
echo ou double-cliquer sur start_api_server.bat
echo.

pause
