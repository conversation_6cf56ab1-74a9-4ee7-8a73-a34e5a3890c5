// ============================================================================
// GÉNÉRATEUR DE CERTIFICATS D'ÉTALONNAGE POUR N8N
// Équivalent JavaScript du script Python extraction.py
// ============================================================================

const XLSX = require('xlsx');
const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');
const Chart = require('chart.js/auto');

// Configuration
const CONFIG = {
    excelFile: 'Application Sheet.xlsx',
    rapportsDir: 'rapports',
    calSheet: 'CAL',
    etaSheet: 'ETA'
};

console.log('🎯 GÉNÉRATEUR DE CERTIFICATS D\'ÉTALONNAGE N8N');
console.log('=' * 60);

// ============================================================================
// FONCTION PRINCIPALE
// ============================================================================
function generateCertificates() {
    try {
        // 1. Lire le fichier Excel
        console.log('📊 Lecture du fichier Excel...');
        const workbook = XLSX.readFile(CONFIG.excelFile);
        
        // 2. Extraire les données
        const calData = extractCalData(workbook);
        const etaData = extractEtaData(workbook);
        
        // 3. Calculer les résultats
        const results = calculateResults(calData, etaData);
        
        // 4. Créer le dossier rapports
        if (!fs.existsSync(CONFIG.rapportsDir)) {
            fs.mkdirSync(CONFIG.rapportsDir);
        }
        
        // 5. Générer les fichiers
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const files = {
            cv: path.join(CONFIG.rapportsDir, `CV_${timestamp}.pdf`),
            ce: path.join(CONFIG.rapportsDir, `CE_${timestamp}.pdf`),
            graph: path.join(CONFIG.rapportsDir, `Graphique_${timestamp}.png`)
        };
        
        // 6. Générer le graphique
        generateGraph(results, files.graph);
        
        // 7. Générer les PDFs
        generateCV(results, files.cv);
        generateCE(results, files.ce, files.graph);
        
        console.log('✅ Génération terminée avec succès !');
        
        return {
            success: true,
            files: files,
            results: results,
            timestamp: timestamp
        };
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
        return {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

// ============================================================================
// EXTRACTION DES DONNÉES CAL
// ============================================================================
function extractCalData(workbook) {
    console.log('🔍 Extraction des données CAL...');
    
    const worksheet = workbook.Sheets[CONFIG.calSheet];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    const calData = {
        client_nom: 'Client non spécifié',
        client_adresse: 'Adresse non spécifiée',
        instrument_type: 'Instrument non spécifié',
        instrument_constructeur: 'Constructeur non spécifié',
        instrument_serie: 'SERIE_INCONNUE',
        instrument_inventaire: 'INV_INCONNU',
        instrument_pmin: 0,
        instrument_pmax: 100,
        instrument_unite: 'unité',
        instrument_division: 0.01,
        instrument_classe: 1,
        date_etalonnage: new Date().toLocaleDateString('fr-FR')
    };
    
    // Parcourir les données pour extraire les informations
    data.forEach(row => {
        if (!row || row.length < 2) return;
        
        const key = String(row[0] || '').trim();
        const value = row[1];
        
        switch (key) {
            case 'DELIVRE A :':
                if (value) calData.client_nom = String(value);
                break;
            case 'ADRESSE : ':
                if (value) calData.client_adresse = String(value);
                break;
            case 'INSTRUMENT ETALONNE :':
                if (value) calData.instrument_type = String(value);
                break;
            case 'Constructeur':
                if (value) calData.instrument_constructeur = String(value);
                break;
            case "N° d'identification ":
                if (value) calData.instrument_inventaire = String(value);
                break;
            case 'Pmax':
                if (typeof value === 'number') calData.instrument_pmax = value;
                break;
            case 'Pmin':
                if (typeof value === 'number') calData.instrument_pmin = value;
                break;
            case 'Unité de pression':
                if (value) calData.instrument_unite = String(value);
                break;
            case 'Echellon ou division d ':
                if (typeof value === 'number') calData.instrument_division = value;
                break;
            case 'Classe ':
                if (typeof value === 'number') calData.instrument_classe = value;
                break;
        }
        
        // Recherche de la date d'étalonnage
        if (key.includes("Date d'étalonnage") && value) {
            try {
                if (typeof value === 'number') {
                    // Date Excel (nombre de jours depuis 1900)
                    const excelDate = new Date((value - 25569) * 86400 * 1000);
                    calData.date_etalonnage = excelDate.toLocaleDateString('fr-FR');
                } else {
                    calData.date_etalonnage = String(value);
                }
            } catch (e) {
                console.warn('⚠️ Erreur conversion date:', e.message);
            }
        }
    });
    
    console.log('✅ Données CAL extraites:', calData);
    return calData;
}

// ============================================================================
// EXTRACTION DES DONNÉES ETA
// ============================================================================
function extractEtaData(workbook) {
    console.log('🔍 Extraction des données ETA...');
    
    const worksheet = workbook.Sheets[CONFIG.etaSheet];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    const etaData = {
        temperature: 20,
        humidite: 50,
        pression_atm: 1013,
        instrument_serie: 'SERIE_INCONNUE',
        mesures_montee: [],
        mesures_descente: [],
        numero_cvp: null,
        numero_cep: null
    };
    
    // Parcourir les données
    data.forEach((row, index) => {
        if (!row || row.length === 0) return;
        
        const key = String(row[0] || '').trim();
        
        // Extraire les conditions d'environnement
        if (key === 'N° de série' && row[1]) {
            etaData.instrument_serie = String(row[1]);
        }
        if (key === 'Température en °C' && typeof row[2] === 'number') {
            etaData.temperature = row[2];
        }
        if (key === 'Humidité relative  en HR%' && typeof row[2] === 'number') {
            etaData.humidite = row[2];
        }
        if (key === 'Pression en hPa' && typeof row[2] === 'number') {
            etaData.pression_atm = row[2];
        }
        
        // Extraire les mesures de montée (lignes 70-85)
        if (index >= 70 && index <= 85 && row.length > 2) {
            if (typeof row[1] === 'number' && typeof row[2] === 'number') {
                etaData.mesures_montee.push({
                    reference: row[1],
                    instrument: row[2],
                    erreur: Math.round((row[2] - row[1]) * 1000) / 1000
                });
            }
        }
        
        // Extraire les mesures de descente (lignes 85-100)
        if (index >= 85 && index <= 100 && row.length > 2) {
            if (typeof row[1] === 'number' && typeof row[2] === 'number') {
                etaData.mesures_descente.push({
                    reference: row[1],
                    instrument: row[2],
                    erreur: Math.round((row[2] - row[1]) * 1000) / 1000
                });
            }
        }
        
        // Rechercher les numéros CVP et CEP
        row.forEach(cell => {
            if (cell && typeof cell === 'string') {
                const cvpMatch = cell.match(/CVP\d+/);
                const cepMatch = cell.match(/CEP\d+/);
                if (cvpMatch && !etaData.numero_cvp) etaData.numero_cvp = cvpMatch[0];
                if (cepMatch && !etaData.numero_cep) etaData.numero_cep = cepMatch[0];
            }
        });
    });
    
    console.log('✅ Données ETA extraites:', {
        ...etaData,
        mesures_montee: `${etaData.mesures_montee.length} points`,
        mesures_descente: `${etaData.mesures_descente.length} points`
    });
    
    return etaData;
}

// ============================================================================
// CALCULS DES RÉSULTATS
// ============================================================================
function calculateResults(calData, etaData) {
    console.log('🧮 Calculs des résultats...');
    
    // Calculer l'erreur maximale
    let erreur_max = 0;
    if (etaData.mesures_montee.length > 0) {
        erreur_max = Math.max(...etaData.mesures_montee.map(m => Math.abs(m.erreur)));
    }
    
    // Calculer l'hystérésis maximale
    let hysteresis_max = 0;
    if (etaData.mesures_montee.length > 0 && etaData.mesures_descente.length > 0) {
        const minLength = Math.min(etaData.mesures_montee.length, etaData.mesures_descente.length);
        for (let i = 0; i < minLength; i++) {
            const montee = etaData.mesures_montee[i].erreur;
            const descente = etaData.mesures_descente[etaData.mesures_descente.length - 1 - i].erreur;
            const hyst = Math.abs(montee - descente);
            if (hyst > hysteresis_max) hysteresis_max = hyst;
        }
    }
    
    // Déterminer la conformité
    const tolerance = calData.instrument_classe;
    const conforme = erreur_max <= tolerance;
    
    // Générer les numéros si nécessaire
    const dateCode = new Date().toISOString().slice(2, 10).replace(/-/g, '');
    const numero_cv = etaData.numero_cvp || `CV${dateCode}`;
    const numero_ce = etaData.numero_cep || `CE${dateCode}`;
    
    const results = {
        ...calData,
        ...etaData,
        erreur_max,
        hysteresis_max,
        tolerance,
        conforme,
        numero_cv,
        numero_ce
    };
    
    console.log('✅ Calculs terminés:', {
        erreur_max: `${erreur_max.toFixed(3)} ${calData.instrument_unite}`,
        hysteresis_max: `${hysteresis_max.toFixed(3)} ${calData.instrument_unite}`,
        conforme: conforme ? '✅ CONFORME' : '❌ NON-CONFORME'
    });
    
    return results;
}

// ============================================================================
// GÉNÉRATION DU GRAPHIQUE
// ============================================================================
function generateGraph(results, outputPath) {
    console.log('📈 Génération du graphique...');

    if (results.mesures_montee.length === 0) {
        console.warn('⚠️ Aucune mesure pour le graphique');
        return false;
    }

    try {
        const canvas = createCanvas(800, 600);
        const ctx = canvas.getContext('2d');

        const pressions = results.mesures_montee.map(m => m.reference);
        const erreurs_montee = results.mesures_montee.map(m => m.erreur);
        const erreurs_descente = results.mesures_descente.map(m => m.erreur);

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: pressions,
                datasets: [{
                    label: 'Erreur montée',
                    data: erreurs_montee,
                    borderColor: 'blue',
                    backgroundColor: 'rgba(0, 0, 255, 0.1)',
                    tension: 0.1
                }, {
                    label: 'Erreur descente',
                    data: erreurs_descente,
                    borderColor: 'red',
                    backgroundColor: 'rgba(255, 0, 0, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: false,
                plugins: {
                    title: {
                        display: true,
                        text: `Courbe d'étalonnage - ${results.instrument_type}`
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: `Pression (${results.instrument_unite})`
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: `Erreur (${results.instrument_unite})`
                        }
                    }
                }
            }
        });

        const buffer = canvas.toBuffer('image/png');
        fs.writeFileSync(outputPath, buffer);

        console.log('✅ Graphique généré:', outputPath);
        return true;

    } catch (error) {
        console.error('❌ Erreur génération graphique:', error.message);
        return false;
    }
}

// ============================================================================
// GÉNÉRATION DU CV (CERTIFICAT DE VÉRIFICATION)
// ============================================================================
function generateCV(results, outputPath) {
    console.log('📄 Génération du CV...');

    try {
        const doc = new PDFDocument({ margin: 50 });
        doc.pipe(fs.createWriteStream(outputPath));

        // En-tête
        doc.fontSize(16).font('Helvetica-Bold')
           .text('LABORATOIRE DE MÉTROLOGIE', { align: 'center' });
        doc.fontSize(12)
           .text('CHAÎNE D\'ÉTALONNAGE - PRESSION', { align: 'center' });
        doc.moveDown(2);

        // Numéro de constat
        doc.fontSize(14).font('Helvetica-Bold')
           .text(`CONSTAT DE VÉRIFICATION N°: ${results.numero_cv}`);
        doc.moveDown();

        // Informations client
        doc.fontSize(12).font('Helvetica-Bold').text('DÉLIVRÉ À');
        doc.font('Helvetica')
           .text(`Client : ${results.client_nom}`)
           .text(`Adresse : ${results.client_adresse}`);
        doc.moveDown();

        // Identification instrument
        doc.font('Helvetica-Bold').text('IDENTIFICATION DE L\'INSTRUMENT');
        doc.font('Helvetica')
           .text(`Désignation : ${results.instrument_type}`)
           .text(`Constructeur : ${results.instrument_constructeur}`)
           .text(`N° de série : ${results.instrument_serie}`)
           .text(`N° d'identification : ${results.instrument_inventaire}`)
           .text(`Plage de mesure : ${results.instrument_pmin} à ${results.instrument_pmax} ${results.instrument_unite}`);
        doc.moveDown();

        // Conditions de vérification
        doc.font('Helvetica-Bold').text('CONDITIONS DE VÉRIFICATION');
        doc.font('Helvetica')
           .text(`Température : ${results.temperature.toFixed(1)} °C`)
           .text(`Humidité relative : ${results.humidite.toFixed(1)} % HR`)
           .text(`Pression atmosphérique : ${results.pression_atm.toFixed(0)} hPa`)
           .text(`Date de vérification : ${results.date_etalonnage}`);
        doc.moveDown();

        // Résultats
        doc.font('Helvetica-Bold').text('RÉSULTATS DE VÉRIFICATION');
        doc.font('Helvetica')
           .text(`Erreur de justesse maximale : ${results.erreur_max.toFixed(3)} ${results.instrument_unite}`)
           .text(`Erreur d'hystérésis maximale : ${results.hysteresis_max.toFixed(3)} ${results.instrument_unite}`)
           .text(`Tolérance : ± ${results.tolerance} ${results.instrument_unite}`);
        doc.moveDown();

        // Jugement
        doc.font('Helvetica-Bold').text('JUGEMENT');
        const jugement = results.conforme ? 'CONFORME' : 'NON-CONFORME';
        doc.font('Helvetica').text(`Instrument ${jugement} aux tolérances`);
        doc.moveDown(2);

        // Signature
        doc.text(`Date d'émission : ${results.date_etalonnage}`);
        doc.moveDown();
        doc.text('Responsable technique : _____________________');
        doc.text('Signature : _____________________');

        doc.end();
        console.log('✅ CV généré:', outputPath);
        return true;

    } catch (error) {
        console.error('❌ Erreur génération CV:', error.message);
        return false;
    }
}

// ============================================================================
// GÉNÉRATION DU CE (CERTIFICAT D'ÉTALONNAGE)
// ============================================================================
function generateCE(results, outputPath, graphPath) {
    console.log('📄 Génération du CE...');

    try {
        const doc = new PDFDocument({ margin: 50 });
        doc.pipe(fs.createWriteStream(outputPath));

        // En-tête
        doc.fontSize(16).font('Helvetica-Bold')
           .text('LABORATOIRE DE MÉTROLOGIE', { align: 'center' });
        doc.fontSize(12)
           .text('CP 20220 Casablanca, Maroc', { align: 'center' });
        doc.moveDown(2);

        // Numéro de certificat
        doc.fontSize(14).font('Helvetica-Bold')
           .text(`CERTIFICAT D'ÉTALONNAGE N° : ${results.numero_ce}`);
        doc.moveDown();

        // Informations client
        doc.fontSize(12).font('Helvetica-Bold').text('DÉLIVRÉ À');
        doc.font('Helvetica')
           .text(`Client : ${results.client_nom}`)
           .text(`Adresse : ${results.client_adresse}`);
        doc.moveDown();

        // Instrument étalonné
        doc.font('Helvetica-Bold').text('INSTRUMENT ÉTALONNÉ');
        const designation = `${results.instrument_type} de ${results.instrument_pmin} à ${results.instrument_pmax} ${results.instrument_unite} ; d=${results.instrument_division} ${results.instrument_unite}`;
        doc.font('Helvetica')
           .text(`Désignation : ${designation}`)
           .text(`Constructeur : ${results.instrument_constructeur}`)
           .text(`N° de série : ${results.instrument_serie}`)
           .text(`N° d'inventaire : ${results.instrument_inventaire}`);
        doc.moveDown();

        // Dates
        doc.font('Helvetica-Bold').text('DATES');
        doc.font('Helvetica')
           .text(`Date d'étalonnage : ${results.date_etalonnage}`)
           .text(`Date d'émission : ${results.date_etalonnage}`);
        doc.moveDown();

        // Conditions d'environnement
        doc.font('Helvetica-Bold').text('CONDITIONS D\'ENVIRONNEMENT');
        doc.font('Helvetica')
           .text(`Température : ${results.temperature.toFixed(1)} °C`)
           .text(`Humidité relative : ${results.humidite.toFixed(1)} % HR`)
           .text(`Pression atmosphérique : ${results.pression_atm.toFixed(0)} hPa`);
        doc.moveDown();

        // Résultats d'étalonnage
        doc.font('Helvetica-Bold').text('RÉSULTATS D\'ÉTALONNAGE');
        doc.font('Helvetica')
           .text(`Erreur de justesse maximale : ${results.erreur_max.toFixed(3)} ${results.instrument_unite}`)
           .text(`Erreur d'hystérésis maximale : ${results.hysteresis_max.toFixed(3)} ${results.instrument_unite}`)
           .text(`Nombre de points de mesure : ${results.mesures_montee.length}`);
        doc.moveDown();

        // Ajouter le graphique si disponible
        if (graphPath && fs.existsSync(graphPath)) {
            doc.addPage();
            doc.font('Helvetica-Bold').text('COURBE D\'ÉTALONNAGE');
            doc.image(graphPath, 50, doc.y + 20, { width: 500 });
        }

        doc.end();
        console.log('✅ CE généré:', outputPath);
        return true;

    } catch (error) {
        console.error('❌ Erreur génération CE:', error.message);
        return false;
    }
}

// Export pour utilisation dans n8n
module.exports = {
    generateCertificates,
    extractCalData,
    extractEtaData,
    calculateResults,
    generateGraph,
    generateCV,
    generateCE
};

// Si exécuté directement
if (require.main === module) {
    generateCertificates();
}
