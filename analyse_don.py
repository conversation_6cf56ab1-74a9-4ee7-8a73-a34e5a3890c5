#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# Lire la feuille DON
don_data = pd.read_excel('Application Sheet.xlsx', sheet_name='DON', header=None)

print("Analyse complète de la feuille DON:")
print("="*50)

# Afficher toutes les lignes avec des données
for i, row in enumerate(don_data.values):
    if any(pd.notna(cell) for cell in row):
        row_data = []
        for cell in row[:10]:  # Limiter aux 10 premières colonnes
            if pd.notna(cell):
                row_data.append(str(cell)[:30])
            else:
                row_data.append("NaN")
        print(f"Ligne {i}: {row_data}")

print("\n" + "="*50)
print("Structure des données importantes:")

# Chercher les sections importantes
for i, row in enumerate(don_data.values):
    if not row or len(row) < 2:
        continue
    
    # Chercher les instruments à étalonner
    if row[1] and "Instrument à étalonner" in str(row[1]):
        print(f"\nSection instruments (ligne {i}):")
        # Afficher les lignes suivantes avec les instruments
        for j in range(i+1, min(i+20, len(don_data))):
            if don_data.iloc[j, 0] and pd.notna(don_data.iloc[j, 0]):
                print(f"  {don_data.iloc[j, 0]}: {don_data.iloc[j, 1]}")
    
    # Chercher les références
    if row[1] and "Référence" in str(row[1]):
        print(f"\nSection références (ligne {i}):")
        # Afficher les lignes suivantes avec les références
        for j in range(i+1, min(i+10, len(don_data))):
            if don_data.iloc[j, 1] and pd.notna(don_data.iloc[j, 1]):
                print(f"  {don_data.iloc[j, 0]}: {don_data.iloc[j, 1]}")
    
    # Chercher les résultats des étalons
    if row[1] and "Résultats des étalons" in str(row[1]):
        print(f"\nSection résultats étalons (ligne {i}):")
        # Afficher les lignes suivantes avec les résultats
        for j in range(i+1, min(i+20, len(don_data))):
            if any(pd.notna(cell) for cell in don_data.iloc[j, :5]):
                print(f"  Ligne {j}: {[str(cell)[:15] if pd.notna(cell) else 'NaN' for cell in don_data.iloc[j, :5]]}")
    
    # Chercher les corrections d'erreurs
    if row[1] and "Correction des erreurs" in str(row[1]):
        print(f"\nSection corrections (ligne {i}):")
        # Afficher les lignes suivantes avec les corrections
        for j in range(i+1, min(i+20, len(don_data))):
            if any(pd.notna(cell) for cell in don_data.iloc[j, :5]):
                print(f"  Ligne {j}: {[str(cell)[:15] if pd.notna(cell) else 'NaN' for cell in don_data.iloc[j, :5]]}")

print("\n" + "="*50)
print("Recherche de données numériques (mesures):")

# Chercher les données numériques (mesures)
for i, row in enumerate(don_data.values):
    if not row:
        continue
    
    # Chercher des lignes avec des valeurs numériques
    valeurs_numeriques = []
    for j, cell in enumerate(row[:10]):
        if pd.notna(cell) and isinstance(cell, (int, float)):
            valeurs_numeriques.append((j, cell))
    
    if valeurs_numeriques:
        print(f"Ligne {i}: {valeurs_numeriques}") 