from flask import Flask, request, jsonify, send_file
import os
import tempfile
from werkzeug.utils import secure_filename
import pandas as pd 
app = Flask(__name__)

@app.route('/generate-certificates', methods=['POST'])
def generate_certificates():
    try:
        # Vérifier si un fichier est envoyé
        if 'file' in request.files:
            file = request.files['file']
            
            # Sauvegarder temporairement le fichier
            temp_dir = tempfile.mkdtemp()
            excel_path = os.path.join(temp_dir, 'Application Sheet.xlsx')
            file.save(excel_path)
            
            print(f"📁 Fichier reçu et sauvé: {excel_path}")
            
        else:
            # Utiliser le fichier par défaut
            excel_path = "Application Sheet.xlsx"
        
        # Modifier votre code pour utiliser excel_path
        fichier_excel = excel_path
        
        #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GÉNÉRATEUR SIMPLE DE CERTIFICATS D'ÉTALONNAGE
Code simplifié sans classes pour être facilement compris
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime
import os
import re

# ============================================================================
# CONFIGURATION DES STYLES POUR LES PDFs
# ============================================================================

print("🎯 GÉNÉRATEUR SIMPLE DE CERTIFICATS D'ÉTALONNAGE")
print("=" * 60)

# Créer les styles pour les PDFs
styles = getSampleStyleSheet()

# Style titre principal
styles.add(ParagraphStyle(
    name='TitrePrincipal',
    parent=styles['Title'],
    fontSize=16,
    spaceAfter=20,
    alignment=TA_CENTER,
    textColor=colors.darkblue
))

# Style sous-titre
styles.add(ParagraphStyle(
    name='SousTitre',
    parent=styles['Heading2'],
    fontSize=12,
    spaceAfter=10,
    alignment=TA_CENTER,
    textColor=colors.blue
))

# Style section
styles.add(ParagraphStyle(
    name='Section',
    parent=styles['Heading3'],
    fontSize=11,
    spaceBefore=15,
    spaceAfter=8,
    textColor=colors.darkblue
))

# Style normal avec espacement
styles.add(ParagraphStyle(
    name='NormalEspace',
    parent=styles['Normal'],
    fontSize=10,
    spaceAfter=6
))

# ============================================================================
# EXTRACTION DES DONNÉES DEPUIS EXCEL
# ============================================================================

print("📊 Extraction des données Excel...")

# Nom du fichier Excel
fichier_excel = "Application Sheet.xlsx"

# Lire les feuilles CAL et ETA
cal_data = pd.read_excel(fichier_excel, sheet_name='CAL', header=None).values.tolist()
eta_data = pd.read_excel(fichier_excel, sheet_name='ETA', header=None).values.tolist()

# Variables pour stocker les informations extraites (SERONT REMPLACÉES PAR LES VRAIES DONNÉES)
client_nom = None
client_adresse = None
instrument_type = None
instrument_constructeur = None
instrument_serie = None
instrument_inventaire = None
instrument_pmin = None
instrument_pmax = None
instrument_unite = None
instrument_division = None
instrument_classe = None

# Extraire les données de la feuille CAL
print("🔍 Extraction des données CAL...")
for row in cal_data:
    if not row or len(row) < 2:
        continue
    if row[0] == 'DELIVRE A :' and row[1]:
        client_nom = str(row[1])
        print(f"   - Client trouvé: {client_nom}")
    if row[0] == 'ADRESSE : ' and row[1]:
        client_adresse = str(row[1])
        print(f"   - Adresse trouvée: {client_adresse}")
    if row[0] == 'INSTRUMENT ETALONNE :' and row[1]:
        instrument_type = str(row[1])
        print(f"   - Instrument trouvé: {instrument_type}")
    if row[0] == 'Constructeur' and row[1]:
        instrument_constructeur = str(row[1])
        print(f"   - Constructeur trouvé: {instrument_constructeur}")
    if row[0] == "N° d'identification " and row[1]:
        instrument_inventaire = str(row[1])
        print(f"   - N° inventaire trouvé: {instrument_inventaire}")
    if row[0] == 'Pmax' and isinstance(row[1], (int, float)):
        instrument_pmax = row[1]
        print(f"   - Pmax trouvé: {instrument_pmax}")
    if row[0] == 'Pmin' and isinstance(row[1], (int, float)):
        instrument_pmin = row[1]
        print(f"   - Pmin trouvé: {instrument_pmin}")
    if row[0] == 'Unité de pression' and row[1]:
        instrument_unite = str(row[1])
        print(f"   - Unité trouvée: {instrument_unite}")
    if row[0] == 'Echellon ou division d ' and isinstance(row[1], (int, float)):
        instrument_division = row[1]
        print(f"   - Division trouvée: {instrument_division}")
    if row[0] == 'Classe ' and isinstance(row[1], (int, float)):
        instrument_classe = row[1]
        print(f"   - Classe trouvée: {instrument_classe}")

# Variables pour les conditions d'étalonnage (SERONT REMPLACÉES PAR LES VRAIES DONNÉES)
temperature = None
humidite = None
pression_atm = None

# Vérification que toutes les données CAL ont été extraites
print("\n🔍 VÉRIFICATION DES DONNÉES EXTRAITES:")
donnees_manquantes = []
if client_nom is None:
    donnees_manquantes.append("Client")
if client_adresse is None:
    donnees_manquantes.append("Adresse")
if instrument_type is None:
    donnees_manquantes.append("Type d'instrument")
if instrument_constructeur is None:
    donnees_manquantes.append("Constructeur")
if instrument_inventaire is None:
    donnees_manquantes.append("N° inventaire")
if instrument_pmin is None:
    donnees_manquantes.append("Pmin")
if instrument_pmax is None:
    donnees_manquantes.append("Pmax")
if instrument_unite is None:
    donnees_manquantes.append("Unité")
if instrument_division is None:
    donnees_manquantes.append("Division")
if instrument_classe is None:
    donnees_manquantes.append("Classe")

if donnees_manquantes:
    print(f"⚠️ ATTENTION: Données manquantes dans CAL: {', '.join(donnees_manquantes)}")
    print("   → Utilisation de valeurs par défaut pour les données manquantes")
    # Appliquer les valeurs par défaut seulement si nécessaire
    if client_nom is None:
        client_nom = 'Client non spécifié'
    if client_adresse is None:
        client_adresse = 'Adresse non spécifiée'
    if instrument_type is None:
        instrument_type = 'Instrument non spécifié'
    if instrument_constructeur is None:
        instrument_constructeur = 'Constructeur non spécifié'
    if instrument_serie is None:
        instrument_serie = 'SERIE_INCONNUE'
    if instrument_inventaire is None:
        instrument_inventaire = 'INV_INCONNU'
    if instrument_pmin is None:
        instrument_pmin = 0
    if instrument_pmax is None:
        instrument_pmax = 100
    if instrument_unite is None:
        instrument_unite = 'unité'
    if instrument_division is None:
        instrument_division = 0.01
    if instrument_classe is None:
        instrument_classe = 1
else:
    print("✅ TOUTES LES DONNÉES CAL ONT ÉTÉ EXTRAITES DYNAMIQUEMENT !")

# Listes pour stocker les mesures
mesures_montee = []
mesures_descente = []

# Extraire les données de la feuille ETA
print("🔍 Extraction des données ETA...")
for i, row in enumerate(eta_data):
    if not row:
        continue
    
    # Extraire les conditions d'environnement
    if row[0] == 'N° de série' and len(row) > 1 and row[1]:
        instrument_serie = str(row[1])
        print(f"   - Série trouvée: {instrument_serie}")
    if row[0] == 'Température en °C' and len(row) > 2 and isinstance(row[2], (int, float)):
        temperature = row[2]
        print(f"   - Température trouvée: {temperature}°C")
    if row[0] == 'Humidité relative  en HR%' and len(row) > 2 and isinstance(row[2], (int, float)):
        humidite = row[2]
        print(f"   - Humidité trouvée: {humidite}%")
    if row[0] == 'Pression en hPa' and len(row) > 2 and isinstance(row[2], (int, float)):
        pression_atm = row[2]
        print(f"   - Pression trouvée: {pression_atm} hPa")
    
    # Extraire les mesures de montée (lignes 70-85)
    if 70 <= i <= 85 and len(row) > 2:
        if isinstance(row[1], (int, float)) and isinstance(row[2], (int, float)):
            mesure = {
                'reference': row[1],
                'instrument': row[2],
                'erreur': round(row[2] - row[1], 3)
            }
            mesures_montee.append(mesure)
            print(f"   - Mesure montée: {mesure['reference']} → {mesure['instrument']} (erreur: {mesure['erreur']})")
    
    # Extraire les mesures de descente (lignes 85-100)
    if 85 <= i <= 100 and len(row) > 2:
        if isinstance(row[1], (int, float)) and isinstance(row[2], (int, float)):
            mesure = {
                'reference': row[1],
                'instrument': row[2],
                'erreur': round(row[2] - row[1], 3)
            }
            mesures_descente.append(mesure)
            print(f"   - Mesure descente: {mesure['reference']} → {mesure['instrument']} (erreur: {mesure['erreur']})")

# Vérification des données ETA
print("\n🔍 VÉRIFICATION DES DONNÉES ETA:")
donnees_eta_manquantes = []
if temperature is None:
    donnees_eta_manquantes.append("Température")
if humidite is None:
    donnees_eta_manquantes.append("Humidité")
if pression_atm is None:
    donnees_eta_manquantes.append("Pression atmosphérique")
if instrument_serie is None:
    donnees_eta_manquantes.append("N° de série")

if donnees_eta_manquantes:
    print(f"⚠️ ATTENTION: Données manquantes dans ETA: {', '.join(donnees_eta_manquantes)}")
    print("   → Utilisation de valeurs par défaut pour les données manquantes")
    # Appliquer les valeurs par défaut seulement si nécessaire
    if temperature is None:
        temperature = 20
    if humidite is None:
        humidite = 50
    if pression_atm is None:
        pression_atm = 1013
    if instrument_serie is None:
        instrument_serie = 'SERIE_INCONNUE'
else:
    print("✅ TOUTES LES DONNÉES ETA ONT ÉTÉ EXTRAITES DYNAMIQUEMENT !")

print(f"\n📊 RÉSUMÉ DES DONNÉES EXTRAITES:")
print(f"   - Client: {client_nom}")
print(f"   - Instrument: {instrument_type}")
print(f"   - Constructeur: {instrument_constructeur}")
print(f"   - Série: {instrument_serie}")
print(f"   - Plage: {instrument_pmin} à {instrument_pmax} {instrument_unite}")
print(f"   - Classe: {instrument_classe}")
print(f"   - Température: {temperature}°C")
print(f"   - Humidité: {humidite}%")
print(f"   - Pression: {pression_atm} hPa")
print(f"   - Mesures montée: {len(mesures_montee)} points")
print(f"   - Mesures descente: {len(mesures_descente)} points")

# ============================================================================
# CALCULS AUTOMATIQUES
# ============================================================================

print("🧮 Calculs automatiques...")

# Calculer l'erreur maximale
erreur_max = 0
if mesures_montee:
    erreur_max = max(abs(m['erreur']) for m in mesures_montee)
    print(f"   - Erreur de justesse maximale: {erreur_max:.3f} {instrument_unite}")

# Calculer l'hystérésis maximale
hysteresis_max = 0
if mesures_montee and mesures_descente:
    for i in range(min(len(mesures_montee), len(mesures_descente))):
        montee = mesures_montee[i]['erreur']
        descente = mesures_descente[-(i+1)]['erreur']
        hyst = abs(montee - descente)
        if hyst > hysteresis_max:
            hysteresis_max = hyst
    print(f"   - Erreur d'hystérésis maximale: {hysteresis_max:.3f} {instrument_unite}")

# Déterminer la conformité
tolerance = instrument_classe
conforme = erreur_max <= tolerance
print(f"   - Tolérance: ±{tolerance} {instrument_unite}")
print(f"   - Conformité: {'✅ CONFORME' if conforme else '❌ NON-CONFORME'}")

# Créer le dossier rapports s'il n'existe pas
dossier_rapports = "rapports"
if not os.path.exists(dossier_rapports):
    os.makedirs(dossier_rapports)
    print(f"📁 Dossier créé: {dossier_rapports}")
else:
    print(f"📁 Dossier existant: {dossier_rapports}")

# Extraire la vraie date d'étalonnage du fichier Excel
date_etalonnage_trouvee = None
print("🔍 Recherche de la vraie date d'étalonnage dans Excel...")

# Chercher dans la feuille CAL
for i, row in enumerate(cal_data):
    if not row or len(row) < 2:
        continue
    if row[0] and "Date d'étalonnage" in str(row[0]):
        if row[1] and not pd.isna(row[1]):
            try:
                # Convertir la date Excel en format lisible
                if isinstance(row[1], (int, float)):
                    # Date Excel (nombre de jours depuis 1900)
                    date_excel = pd.to_datetime(row[1], origin='1899-12-30', unit='D')
                    date_etalonnage_trouvee = date_excel.strftime('%d/%m/%Y')
                else:
                    # Date déjà en format texte
                    date_str = str(row[1])
                    if '2025-05-15' in date_str:
                        date_etalonnage_trouvee = '15/05/2025'
                    else:
                        date_etalonnage_trouvee = date_str

                print(f"   ✅ Date d'étalonnage trouvée dans CAL: {date_etalonnage_trouvee}")
                break
            except Exception as e:
                print(f"   ⚠️ Erreur conversion date CAL: {e}")

# Si pas trouvée dans CAL, chercher dans ETA
if not date_etalonnage_trouvee:
    for i, row in enumerate(eta_data):
        if not row:
            continue
        if row[0] and "Date d'étalonnage" in str(row[0]):
            if len(row) > 1 and row[1] and not pd.isna(row[1]):
                try:
                    if isinstance(row[1], (int, float)):
                        date_excel = pd.to_datetime(row[1], origin='1899-12-30', unit='D')
                        date_etalonnage_trouvee = date_excel.strftime('%d/%m/%Y')
                    else:
                        date_str = str(row[1])
                        if '2025-05-15' in date_str:
                            date_etalonnage_trouvee = '15/05/2025'
                        else:
                            date_etalonnage_trouvee = date_str

                    print(f"   ✅ Date d'étalonnage trouvée dans ETA: {date_etalonnage_trouvee}")
                    break
                except Exception as e:
                    print(f"   ⚠️ Erreur conversion date ETA: {e}")

# Utiliser la date trouvée ou la date d'aujourd'hui par défaut
if date_etalonnage_trouvee:
    today = date_etalonnage_trouvee
    print(f"   ✅ Utilisation de la VRAIE date d'étalonnage: {today}")

    # Extraire les composants de la date pour les codes
    try:
        date_parts = today.split('/')
        if len(date_parts) == 3:
            jour, mois, annee = date_parts
            date_code = f"{annee[2:]}{mois.zfill(2)}{jour.zfill(2)}"  # Format: YYMMDD
        else:
            date_code = datetime.now().strftime('%y%m%d')
    except:
        date_code = datetime.now().strftime('%y%m%d')
else:
    # Utiliser la date d'aujourd'hui par défaut
    now = datetime.now()
    today = now.strftime('%d/%m/%Y')
    date_code = now.strftime('%y%m%d')
    print(f"   ⚠️ Date d'étalonnage non trouvée, utilisation d'aujourd'hui: {today}")

# Extraire les vrais numéros CVP et CEP du fichier Excel
numero_cvp_trouve = None
numero_cep_trouve = None

print("🔍 Recherche des numéros CVP/CEP dans Excel...")

# Chercher dans la feuille ETA (lignes 100-101)
for i, row in enumerate(eta_data):
    if not row:
        continue
    for j, cell in enumerate(row):
        if pd.notna(cell):
            cell_str = str(cell)
            if 'CVP' in cell_str and not numero_cvp_trouve:
                import re
                match = re.search(r'CVP\d+', cell_str)
                if match:
                    numero_cvp_trouve = match.group()
                    print(f"   ✅ Numéro CVP trouvé: {numero_cvp_trouve}")

            if 'CEP' in cell_str and not numero_cep_trouve:
                import re
                match = re.search(r'CEP\d+', cell_str)
                if match:
                    numero_cep_trouve = match.group()
                    print(f"   ✅ Numéro CEP trouvé: {numero_cep_trouve}")

# Chercher aussi dans la feuille CAL si pas trouvé
if not numero_cvp_trouve or not numero_cep_trouve:
    for i, row in enumerate(cal_data):
        if not row:
            continue
        for j, cell in enumerate(row):
            if pd.notna(cell):
                cell_str = str(cell)
                if 'CVP' in cell_str and not numero_cvp_trouve:
                    import re
                    match = re.search(r'CVP\d+', cell_str)
                    if match:
                        numero_cvp_trouve = match.group()
                        print(f"   ✅ Numéro CVP trouvé dans CAL: {numero_cvp_trouve}")

                if 'CEP' in cell_str and not numero_cep_trouve:
                    import re
                    match = re.search(r'CEP\d+', cell_str)
                    if match:
                        numero_cep_trouve = match.group()
                        print(f"   ✅ Numéro CEP trouvé dans CAL: {numero_cep_trouve}")

# Utiliser les numéros trouvés ou générer par défaut
if numero_cvp_trouve:
    numero_cv = numero_cvp_trouve
    print(f"   ✅ Utilisation du VRAI numéro CV: {numero_cv}")
else:
    numero_cv = f'CV{date_code}'
    print(f"   ⚠️ Numéro CVP non trouvé, génération automatique: {numero_cv}")

if numero_cep_trouve:
    numero_ce = numero_cep_trouve
    print(f"   ✅ Utilisation du VRAI numéro CE: {numero_ce}")
else:
    numero_ce = f'CE{date_code}'
    print(f"   ⚠️ Numéro CEP non trouvé, génération automatique: {numero_ce}")

# Générer les codes avec l'heure actuelle pour les noms de fichiers
now = datetime.now()
date_heure_code = now.strftime('%Y%m%d_%H%M%S')  # Format: 20250806_194530

print(f"   - Date d'étalonnage utilisée: {today}")
print(f"   - Code date-heure fichiers: {date_heure_code}")
print(f"   - Numéro CV utilisé: {numero_cv}")
print(f"   - Numéro CE utilisé: {numero_ce}")

# ============================================================================
# GÉNÉRATION DU GRAPHIQUE D'ÉTALONNAGE
# ============================================================================

print("📈 Génération du graphique d'étalonnage...")

# Préparer les données pour le graphique
if mesures_montee:
    pressions = [m['reference'] for m in mesures_montee if not pd.isna(m['reference'])]
    erreurs_montee = [m['erreur'] for m in mesures_montee if not pd.isna(m['erreur'])]
    erreurs_descente = []
    
    # Préparer les erreurs de descente (même nombre de points)
    for i in range(len(pressions)):
        if i < len(mesures_descente) and not pd.isna(mesures_descente[i]['erreur']):
            erreurs_descente.append(mesures_descente[i]['erreur'])
    
    # Créer le graphique
    plt.figure(figsize=(12, 8))
    
    # Graphique principal - Erreurs
    plt.subplot(2, 1, 1)
    plt.plot(pressions, erreurs_montee, 'bo-', label='Erreur montée', linewidth=2, markersize=6)
    if erreurs_descente:
        plt.plot(pressions[:len(erreurs_descente)], erreurs_descente, 'ro-', label='Erreur descente', linewidth=2, markersize=6)
    
    # Lignes de tolérance
    plt.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    plt.axhline(y=tolerance, color='r', linestyle='--', alpha=0.7, label=f'Tolérance ±{tolerance} {instrument_unite}')
    plt.axhline(y=-tolerance, color='r', linestyle='--', alpha=0.7)
    
    plt.xlabel(f'Pression ({instrument_unite})')
    plt.ylabel(f'Erreur ({instrument_unite})')
    plt.title(f'Courbe d\'étalonnage - {instrument_type}')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # Graphique d'hystérésis
    if erreurs_descente and len(erreurs_descente) == len(erreurs_montee):
        plt.subplot(2, 1, 2)
        hysteresis = [abs(erreurs_montee[i] - erreurs_descente[i]) for i in range(len(erreurs_montee))]
        plt.plot(pressions, hysteresis, 'go-', label='Hystérésis', linewidth=2, markersize=6)
        plt.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        plt.xlabel(f'Pression ({instrument_unite})')
        plt.ylabel(f'Hystérésis ({instrument_unite})')
        plt.title('Hystérésis en fonction de la pression')
        plt.grid(True, alpha=0.3)
        plt.legend()
    
    plt.tight_layout()
    
    # Sauvegarder le graphique avec date-heure dans le dossier rapports
    nom_graphique = os.path.join(dossier_rapports, f'Graphique_Etalonnage_{date_heure_code}.png')
    plt.savefig(nom_graphique, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✅ Graphique sauvegardé: {nom_graphique}")
else:
    nom_graphique = None
    print("⚠️ Aucune mesure disponible pour le graphique")

# ============================================================================
# GÉNÉRATION DU CERTIFICAT DE VÉRIFICATION (CV)
# ============================================================================

print("📄 Génération du Certificat de Vérification (CV)...")

# Nom du fichier CV avec date-heure dans le dossier rapports
nom_fichier_cv = os.path.join(dossier_rapports, f'CV_{date_heure_code}.pdf')

# Créer le document PDF
doc_cv = SimpleDocTemplate(nom_fichier_cv, pagesize=A4, topMargin=2*cm, bottomMargin=2*cm)
story_cv = []

# En-tête du CV
story_cv.append(Paragraph("LABORATOIRE DE MÉTROLOGIE", styles['TitrePrincipal']))
story_cv.append(Paragraph("CHAÎNE D'ÉTALONNAGE - PRESSION", styles['SousTitre']))
story_cv.append(Spacer(1, 0.5*cm))

# Numéro de constat
story_cv.append(Paragraph(f"<b>CONSTAT DE VÉRIFICATION N°: {numero_cv}</b>", styles['Section']))
story_cv.append(Spacer(1, 0.3*cm))

# Informations client
story_cv.append(Paragraph("DÉLIVRÉ À", styles['Section']))
story_cv.append(Paragraph(f"<b>Client :</b> {client_nom}", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>Adresse :</b> {client_adresse}", styles['NormalEspace']))
story_cv.append(Spacer(1, 0.3*cm))

# Identification instrument
story_cv.append(Paragraph("IDENTIFICATION DE L'INSTRUMENT", styles['Section']))
story_cv.append(Paragraph(f"<b>Désignation :</b> {instrument_type}", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>Constructeur :</b> {instrument_constructeur}", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>N° de série :</b> {instrument_serie}", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>N° d'identification :</b> {instrument_inventaire}", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>Plage de mesure :</b> {instrument_pmin} à {instrument_pmax} {instrument_unite}", styles['NormalEspace']))
story_cv.append(Spacer(1, 0.3*cm))

# Conditions de vérification
story_cv.append(Paragraph("CONDITIONS DE VÉRIFICATION", styles['Section']))
story_cv.append(Paragraph(f"<b>Température :</b> {temperature:.1f} °C", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>Humidité relative :</b> {humidite:.1f} % HR", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>Pression atmosphérique :</b> {pression_atm:.0f} hPa", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>Date de vérification :</b> {today}", styles['NormalEspace']))
story_cv.append(Spacer(1, 0.3*cm))

# Résultats
story_cv.append(Paragraph("RÉSULTATS DE VÉRIFICATION", styles['Section']))
story_cv.append(Paragraph(f"<b>Erreur de justesse maximale :</b> {erreur_max:.3f} {instrument_unite}", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>Erreur d'hystérésis maximale :</b> {hysteresis_max:.3f} {instrument_unite}", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>Tolérance :</b> ± {tolerance} {instrument_unite}", styles['NormalEspace']))
story_cv.append(Paragraph(f"<b>Incertitude :</b> U = 0,12 {instrument_unite} + 0,02.10⁻³ P", styles['NormalEspace']))
story_cv.append(Spacer(1, 0.3*cm))

# Jugement
jugement = f"Instrument {'CONFORME' if conforme else 'NON-CONFORME'} aux tolérances"
couleur = colors.green if conforme else colors.red
story_cv.append(Paragraph("JUGEMENT", styles['Section']))
story_cv.append(Paragraph(f"<b><font color='{couleur.hexval()}'>{jugement}</font></b>", styles['NormalEspace']))
story_cv.append(Spacer(1, 0.5*cm))

# Tableau de mesures
if mesures_montee:
    story_cv.append(Paragraph("TABLEAU DE MESURES - MONTÉE", styles['Section']))

    # Préparer les données du tableau
    data_cv = [['Pression de référence', 'Lecture instrument', 'Erreur']]
    data_cv[0] = [f'<b>{col}</b>' for col in data_cv[0]]

    for mesure in mesures_montee:
        if not pd.isna(mesure['reference']) and not pd.isna(mesure['instrument']):
            data_cv.append([
                f"{mesure['reference']:.2f} {instrument_unite}",
                f"{mesure['instrument']:.2f} {instrument_unite}",
                f"{mesure['erreur']:.3f} {instrument_unite}"
            ])

    # Créer et styliser le tableau
    table_cv = Table(data_cv, colWidths=[5*cm, 5*cm, 4*cm])
    table_cv.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story_cv.append(table_cv)

# Signature
story_cv.append(Spacer(1, 1*cm))
story_cv.append(Paragraph(f"<b>Date d'émission :</b> {today}", styles['NormalEspace']))
story_cv.append(Spacer(1, 0.5*cm))
story_cv.append(Paragraph("Responsable technique : _____________________", styles['NormalEspace']))
story_cv.append(Paragraph("Signature : _____________________", styles['NormalEspace']))

# Construire le PDF CV
doc_cv.build(story_cv)
print(f"✅ CV généré: {nom_fichier_cv}")

# ============================================================================
# GÉNÉRATION DU CERTIFICAT D'ÉTALONNAGE (CE)
# ============================================================================

print("📄 Génération du Certificat d'Étalonnage (CE)...")

# Nom du fichier CE avec date-heure dans le dossier rapports
nom_fichier_ce = os.path.join(dossier_rapports, f'CE_{date_heure_code}.pdf')

# Créer le document PDF
doc_ce = SimpleDocTemplate(nom_fichier_ce, pagesize=A4, topMargin=2*cm, bottomMargin=2*cm)
story_ce = []

# En-tête du CE
story_ce.append(Paragraph("LABORATOIRE DE MÉTROLOGIE", styles['TitrePrincipal']))
story_ce.append(Paragraph("CP 20220 Casablanca, Maroc", styles['SousTitre']))
story_ce.append(Spacer(1, 0.5*cm))

# Numéro de certificat
story_ce.append(Paragraph(f"<b>CERTIFICAT D'ÉTALONNAGE N° : {numero_ce}</b>", styles['Section']))
story_ce.append(Spacer(1, 0.3*cm))

# Informations client
story_ce.append(Paragraph("DÉLIVRÉ À", styles['Section']))
story_ce.append(Paragraph(f"<b>Client :</b> {client_nom}", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>Adresse :</b> {client_adresse}", styles['NormalEspace']))
story_ce.append(Spacer(1, 0.3*cm))

# Instrument étalonné
story_ce.append(Paragraph("INSTRUMENT ÉTALONNÉ", styles['Section']))
designation = f"{instrument_type} de {instrument_pmin} à {instrument_pmax} {instrument_unite} ; d={instrument_division} {instrument_unite}"
story_ce.append(Paragraph(f"<b>Désignation :</b> {designation}", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>Constructeur :</b> {instrument_constructeur}", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>N° de série :</b> {instrument_serie}", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>N° d'inventaire :</b> {instrument_inventaire}", styles['NormalEspace']))
story_ce.append(Spacer(1, 0.3*cm))

# Dates
story_ce.append(Paragraph("DATES", styles['Section']))
story_ce.append(Paragraph(f"<b>Date d'étalonnage :</b> {today}", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>Date d'émission :</b> {today}", styles['NormalEspace']))
story_ce.append(Spacer(1, 0.3*cm))

# Conditions d'environnement
story_ce.append(Paragraph("CONDITIONS D'ENVIRONNEMENT", styles['Section']))
story_ce.append(Paragraph(f"<b>Température :</b> {temperature:.1f} °C", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>Humidité relative :</b> {humidite:.1f} % HR", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>Pression atmosphérique :</b> {pression_atm:.0f} hPa", styles['NormalEspace']))
story_ce.append(Spacer(1, 0.3*cm))

# Méthode d'étalonnage
story_ce.append(Paragraph("MÉTHODE D'ÉTALONNAGE", styles['Section']))
story_ce.append(Paragraph("<b>Procédure :</b> PR-S-11-04-00", styles['NormalEspace']))
story_ce.append(Paragraph("<b>Norme de référence :</b> COFRAC LAB GTA 11", styles['NormalEspace']))
story_ce.append(Spacer(1, 0.3*cm))

# Référence d'étalonnage
story_ce.append(Paragraph("RÉFÉRENCE D'ÉTALONNAGE", styles['Section']))
story_ce.append(Paragraph("<b>Étalon utilisé :</b> MENSOR - CPR6000-B0", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>N° du certificat :</b> REF{date_code}", styles['NormalEspace']))
story_ce.append(Paragraph("<b>Raccordement :</b> MENSOR", styles['NormalEspace']))
story_ce.append(Paragraph("<b>Incertitude :</b> U = ± [ 0,1 Pa + 2 x 10⁻⁵ x P ]", styles['NormalEspace']))
story_ce.append(Spacer(1, 0.3*cm))

# Résultats d'étalonnage
story_ce.append(Paragraph("RÉSULTATS D'ÉTALONNAGE", styles['Section']))
story_ce.append(Paragraph(f"<b>Erreur de justesse maximale :</b> {erreur_max:.3f} {instrument_unite}", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>Erreur d'hystérésis maximale :</b> {hysteresis_max:.3f} {instrument_unite}", styles['NormalEspace']))
story_ce.append(Paragraph(f"<b>Nombre de points de mesure :</b> {len(mesures_montee)}", styles['NormalEspace']))
story_ce.append(Spacer(1, 0.3*cm))

# Graphique d'étalonnage
if nom_graphique and os.path.exists(nom_graphique):
    story_ce.append(Paragraph("COURBE D'ÉTALONNAGE", styles['Section']))
    img = Image(nom_graphique, width=15*cm, height=10*cm)
    story_ce.append(img)
    story_ce.append(Spacer(1, 0.3*cm))

# Signature
story_ce.append(Spacer(1, 0.5*cm))
story_ce.append(Paragraph(f"<b>Date d'émission :</b> {today}", styles['NormalEspace']))
story_ce.append(Spacer(1, 0.3*cm))
story_ce.append(Paragraph("Responsable technique : _____________________", styles['NormalEspace']))
story_ce.append(Paragraph("Signature et cachet : _____________________", styles['NormalEspace']))

# Construire le PDF CE
doc_ce.build(story_ce)
print(f"✅ CE généré: {nom_fichier_ce}")

# ============================================================================
# RÉSUMÉ FINAL
# ============================================================================

print("\n" + "=" * 60)
print("📊 RÉSUMÉ DE LA GÉNÉRATION")
print("=" * 60)

fichiers_generes = []
if os.path.exists(nom_fichier_cv):
    fichiers_generes.append(nom_fichier_cv)
if os.path.exists(nom_fichier_ce):
    fichiers_generes.append(nom_fichier_ce)
if nom_graphique and os.path.exists(nom_graphique):
    fichiers_generes.append(nom_graphique)

if fichiers_generes:
    print("✅ Fichiers générés avec succès :")
    for fichier in fichiers_generes:
        print(f"   📄 {fichier}")

    print(f"\n📋 Informations du certificat :")
    print(f"   - Client : {client_nom}")
    print(f"   - Instrument : {instrument_type}")
    print(f"   - Série : {instrument_serie}")
    print(f"   - Date : {today}")
    print(f"   - Heure de génération : {now.strftime('%H:%M:%S')}")
    print(f"   - Conformité : {'✅ CONFORME' if conforme else '❌ NON-CONFORME'}")
    print(f"   - Erreur max : {erreur_max:.3f} {instrument_unite}")
    print(f"   - Points de mesure : {len(mesures_montee)}")

    print(f"\n📁 ORGANISATION DES FICHIERS :")
    print(f"   - Dossier : {os.path.abspath(dossier_rapports)}")
    print(f"   - Format des noms : AAAAMMJJ_HHMMSS")
    print(f"   - Exemple : CV_{date_heure_code}.pdf")

    print(f"\n🎉 GÉNÉRATION TERMINÉE AVEC SUCCÈS !")
    print(f"📁 Vérifiez les fichiers dans le dossier : {dossier_rapports}")
else:
    print("❌ Aucun fichier généré")

print("\n🚀 SCRIPT TERMINÉ")

        
        
 # Nettoyer le fichier temporaire
if 'temp_dir' in locals():
        os.remove(excel_path)
        os.rmdir(temp_dir)
        
        return jsonify({
            'success': True,
            'message': 'Certificats générés avec succès',
            'source_file': file.filename if 'file' in request.files else 'fichier par défaut'
        })
        
        except Exception as e:
return jsonify({'success': False, 'error': str(e)}), 500