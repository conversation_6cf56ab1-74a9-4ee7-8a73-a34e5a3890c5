#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API SERVER POUR GÉNÉRATION DE CERTIFICATS D'ÉTALONNAGE
Serveur HTTP pour utilisation avec n8n
Équivalent API du script extraction.py
"""

from flask import Flask, request, jsonify, send_file
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime
import os
import re
import io
import base64
import zipfile
import tempfile
import json

app = Flask(__name__)

# Configuration des styles PDF (identique à extraction.py)
styles = getSampleStyleSheet()

styles.add(ParagraphStyle(
    name='TitrePrincipal',
    parent=styles['Title'],
    fontSize=16,
    spaceAfter=20,
    alignment=TA_CENTER,
    textColor=colors.darkblue
))

styles.add(ParagraphStyle(
    name='SousTitre',
    parent=styles['Heading2'],
    fontSize=12,
    spaceAfter=10,
    alignment=TA_CENTER,
    textColor=colors.blue
))

styles.add(ParagraphStyle(
    name='Section',
    parent=styles['Heading3'],
    fontSize=11,
    spaceBefore=15,
    spaceAfter=8,
    textColor=colors.darkblue
))

styles.add(ParagraphStyle(
    name='NormalEspace',
    parent=styles['Normal'],
    fontSize=10,
    spaceAfter=6
))

@app.route('/health', methods=['GET'])
def health_check():
    """Point de santé de l'API"""
    return jsonify({
        "status": "healthy",
        "service": "Générateur de Certificats d'Étalonnage",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/test-excel', methods=['POST'])
def test_excel():
    """Endpoint de test pour vérifier le fichier Excel"""
    try:
        print("🧪 Test de réception du fichier Excel...")

        excel_data = None

        if 'file' in request.files:
            file = request.files['file']
            excel_data = file.read()
            print(f"📁 Fichier multipart reçu: {len(excel_data)} bytes")

        elif request.json and 'excel_base64' in request.json:
            excel_base64 = request.json['excel_base64']
            excel_data = base64.b64decode(excel_base64)
            print(f"📁 Fichier base64 reçu: {len(excel_data)} bytes")

        if excel_data:
            # Vérifier la signature
            signature = excel_data[:4].hex() if len(excel_data) >= 4 else "vide"

            return jsonify({
                "success": True,
                "fileSize": len(excel_data),
                "signature": signature,
                "isExcel": excel_data[:2] == b'PK',
                "message": "Fichier reçu et analysé"
            })
        else:
            return jsonify({
                "success": False,
                "error": "Aucun fichier reçu"
            }), 400

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/generate-certificates', methods=['POST'])
def generate_certificates():
    """
    Endpoint principal pour générer les certificats
    Attend un fichier Excel en base64 ou multipart
    """
    try:
        print("🚀 Nouvelle demande de génération de certificats")
        
        # Récupérer le fichier Excel
        excel_data = None

        if 'file' in request.files:
            # Fichier envoyé via multipart/form-data
            file = request.files['file']
            excel_data = file.read()
            print("📁 Fichier reçu via multipart")

        elif request.json and 'excel_base64' in request.json:
            # Fichier envoyé en base64 dans JSON
            excel_base64 = request.json['excel_base64']
            print(f"📁 Base64 reçu, taille: {len(excel_base64)} caractères")

            try:
                excel_data = base64.b64decode(excel_base64)
                print(f"📁 Décodage base64 réussi, taille: {len(excel_data)} bytes")

                # Vérifier la signature du fichier Excel
                if len(excel_data) >= 2:
                    signature = excel_data[:2]
                    print(f"🔍 Signature fichier: {signature}")
                    if signature != b'PK':
                        print("⚠️ Attention: Signature Excel non reconnue")

            except Exception as decode_error:
                print(f"❌ Erreur décodage base64: {decode_error}")
                return jsonify({
                    "success": False,
                    "error": f"Erreur décodage base64: {str(decode_error)}"
                }), 400

        else:
            return jsonify({
                "success": False,
                "error": "Aucun fichier Excel fourni. Utilisez 'file' (multipart) ou 'excel_base64' (JSON)"
            }), 400
        
        # Sauvegarder temporairement le fichier Excel
        print(f"📁 Taille des données Excel reçues: {len(excel_data)} bytes")

        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file.write(excel_data)
            temp_excel_path = temp_file.name

        print(f"📁 Fichier temporaire créé: {temp_excel_path}")
        print(f"📁 Taille du fichier: {os.path.getsize(temp_excel_path)} bytes")
        
        try:
            # Traiter le fichier Excel
            result = process_excel_file(temp_excel_path)
            
            if result['success']:
                print("✅ Traitement terminé avec succès")
                return jsonify(result)
            else:
                print(f"❌ Erreur de traitement: {result['error']}")
                return jsonify(result), 500
                
        finally:
            # Nettoyer le fichier temporaire
            if os.path.exists(temp_excel_path):
                os.unlink(temp_excel_path)
        
    except Exception as e:
        print(f"❌ Erreur serveur: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Erreur serveur: {str(e)}"
        }), 500

def process_excel_file(excel_path):
    """
    Traite le fichier Excel et génère les certificats
    Logique identique à extraction.py
    """
    try:
        print("📊 Lecture du fichier Excel...")
        
        # Lire les feuilles CAL et ETA avec engine spécifié
        try:
            cal_data = pd.read_excel(excel_path, sheet_name='CAL', header=None, engine='openpyxl').values.tolist()
            eta_data = pd.read_excel(excel_path, sheet_name='ETA', header=None, engine='openpyxl').values.tolist()
            print("✅ Fichier Excel lu avec openpyxl")
        except Exception as e1:
            print(f"⚠️ Erreur avec openpyxl: {e1}")
            try:
                cal_data = pd.read_excel(excel_path, sheet_name='CAL', header=None, engine='xlrd').values.tolist()
                eta_data = pd.read_excel(excel_path, sheet_name='ETA', header=None, engine='xlrd').values.tolist()
                print("✅ Fichier Excel lu avec xlrd")
            except Exception as e2:
                print(f"⚠️ Erreur avec xlrd: {e2}")
                # Essayer sans spécifier d'engine
                cal_data = pd.read_excel(excel_path, sheet_name='CAL', header=None).values.tolist()
                eta_data = pd.read_excel(excel_path, sheet_name='ETA', header=None).values.tolist()
                print("✅ Fichier Excel lu sans engine spécifique")
        
        # Extraire les données CAL (logique identique à extraction.py)
        cal_info = extract_cal_data(cal_data)
        
        # Extraire les données ETA (logique identique à extraction.py)
        eta_info = extract_eta_data(eta_data)
        
        # Fusionner les données
        all_data = {**cal_info, **eta_info}
        
        # Calculer les résultats
        results = calculate_results(all_data)
        
        # Générer les fichiers
        files = generate_all_files(results)
        
        return {
            "success": True,
            "data": results,
            "files": files,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

def extract_cal_data(cal_data):
    """Extraction des données CAL (identique à extraction.py)"""
    print("🔍 Extraction des données CAL...")
    
    cal_info = {
        'client_nom': 'Client non spécifié',
        'client_adresse': 'Adresse non spécifiée',
        'instrument_type': 'Instrument non spécifié',
        'instrument_constructeur': 'Constructeur non spécifié',
        'instrument_inventaire': 'INV_INCONNU',
        'instrument_pmin': 0,
        'instrument_pmax': 100,
        'instrument_unite': 'unité',
        'instrument_division': 0.01,
        'instrument_classe': 1,
        'date_etalonnage': datetime.now().strftime('%d/%m/%Y')
    }
    
    # Logique d'extraction identique à extraction.py
    for row in cal_data:
        if not row or len(row) < 2:
            continue
        if row[0] == 'DELIVRE A :' and row[1]:
            cal_info['client_nom'] = str(row[1])
        if row[0] == 'ADRESSE : ' and row[1]:
            cal_info['client_adresse'] = str(row[1])
        if row[0] == 'INSTRUMENT ETALONNE :' and row[1]:
            cal_info['instrument_type'] = str(row[1])
        if row[0] == 'Constructeur' and row[1]:
            cal_info['instrument_constructeur'] = str(row[1])
        if row[0] == "N° d'identification " and row[1]:
            cal_info['instrument_inventaire'] = str(row[1])
        if row[0] == 'Pmax' and isinstance(row[1], (int, float)):
            cal_info['instrument_pmax'] = row[1]
        if row[0] == 'Pmin' and isinstance(row[1], (int, float)):
            cal_info['instrument_pmin'] = row[1]
        if row[0] == 'Unité de pression' and row[1]:
            cal_info['instrument_unite'] = str(row[1])
        if row[0] == 'Echellon ou division d ' and isinstance(row[1], (int, float)):
            cal_info['instrument_division'] = row[1]
        if row[0] == 'Classe ' and isinstance(row[1], (int, float)):
            cal_info['instrument_classe'] = row[1]
        
        # Extraction de la date d'étalonnage
        if row[0] and "Date d'étalonnage" in str(row[0]) and row[1]:
            try:
                if isinstance(row[1], (int, float)):
                    date_excel = pd.to_datetime(row[1], origin='1899-12-30', unit='D')
                    cal_info['date_etalonnage'] = date_excel.strftime('%d/%m/%Y')
                else:
                    cal_info['date_etalonnage'] = str(row[1])
            except:
                pass
    
    return cal_info

def extract_eta_data(eta_data):
    """Extraction des données ETA (identique à extraction.py)"""
    print("🔍 Extraction des données ETA...")
    
    eta_info = {
        'instrument_serie': 'SERIE_INCONNUE',
        'temperature': 20,
        'humidite': 50,
        'pression_atm': 1013,
        'mesures_montee': [],
        'mesures_descente': [],
        'numero_cvp': None,
        'numero_cep': None
    }
    
    # Logique d'extraction identique à extraction.py
    for i, row in enumerate(eta_data):
        if not row:
            continue
        
        # Conditions d'environnement
        if row[0] == 'N° de série' and len(row) > 1 and row[1]:
            eta_info['instrument_serie'] = str(row[1])
        if row[0] == 'Température en °C' and len(row) > 2 and isinstance(row[2], (int, float)):
            eta_info['temperature'] = row[2]
        if row[0] == 'Humidité relative  en HR%' and len(row) > 2 and isinstance(row[2], (int, float)):
            eta_info['humidite'] = row[2]
        if row[0] == 'Pression en hPa' and len(row) > 2 and isinstance(row[2], (int, float)):
            eta_info['pression_atm'] = row[2]
        
        # Mesures de montée (lignes 70-85)
        if 70 <= i <= 85 and len(row) > 2:
            if isinstance(row[1], (int, float)) and isinstance(row[2], (int, float)):
                eta_info['mesures_montee'].append({
                    'reference': row[1],
                    'instrument': row[2],
                    'erreur': round(row[2] - row[1], 3)
                })
        
        # Mesures de descente (lignes 85-100)
        if 85 <= i <= 100 and len(row) > 2:
            if isinstance(row[1], (int, float)) and isinstance(row[2], (int, float)):
                eta_info['mesures_descente'].append({
                    'reference': row[1],
                    'instrument': row[2],
                    'erreur': round(row[2] - row[1], 3)
                })
        
        # Recherche des numéros CVP/CEP
        for cell in row:
            if pd.notna(cell):
                cell_str = str(cell)
                if 'CVP' in cell_str and not eta_info['numero_cvp']:
                    match = re.search(r'CVP\d+', cell_str)
                    if match:
                        eta_info['numero_cvp'] = match.group()
                if 'CEP' in cell_str and not eta_info['numero_cep']:
                    match = re.search(r'CEP\d+', cell_str)
                    if match:
                        eta_info['numero_cep'] = match.group()
    
    return eta_info

def calculate_results(data):
    """Calculs des résultats (identique à extraction.py)"""
    print("🧮 Calculs des résultats...")
    
    # Calculer l'erreur maximale
    erreur_max = 0
    if data['mesures_montee']:
        erreur_max = max(abs(m['erreur']) for m in data['mesures_montee'])
    
    # Calculer l'hystérésis maximale
    hysteresis_max = 0
    if data['mesures_montee'] and data['mesures_descente']:
        for i in range(min(len(data['mesures_montee']), len(data['mesures_descente']))):
            montee = data['mesures_montee'][i]['erreur']
            descente = data['mesures_descente'][-(i+1)]['erreur']
            hyst = abs(montee - descente)
            if hyst > hysteresis_max:
                hysteresis_max = hyst
    
    # Conformité
    tolerance = data['instrument_classe']
    conforme = erreur_max <= tolerance
    
    # Générer les numéros si nécessaire
    date_code = datetime.now().strftime('%y%m%d')
    numero_cv = data['numero_cvp'] or f'CV{date_code}'
    numero_ce = data['numero_cep'] or f'CE{date_code}'
    
    return {
        **data,
        'erreur_max': erreur_max,
        'hysteresis_max': hysteresis_max,
        'tolerance': tolerance,
        'conforme': conforme,
        'numero_cv': numero_cv,
        'numero_ce': numero_ce
    }

def generate_all_files(results):
    """Génère tous les fichiers et retourne en base64"""
    print("📄 Génération des fichiers...")
    
    files = {}
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    try:
        # Générer le graphique
        graph_buffer = generate_graph_buffer(results)
        if graph_buffer:
            files['graphique'] = {
                'filename': f'Graphique_Etalonnage_{timestamp}.png',
                'content': base64.b64encode(graph_buffer.getvalue()).decode('utf-8'),
                'type': 'image/png'
            }
        
        # Générer le CV
        cv_buffer = generate_cv_buffer(results)
        files['cv'] = {
            'filename': f'CV_{timestamp}.pdf',
            'content': base64.b64encode(cv_buffer.getvalue()).decode('utf-8'),
            'type': 'application/pdf'
        }
        
        # Générer le CE
        ce_buffer = generate_ce_buffer(results, graph_buffer)
        files['ce'] = {
            'filename': f'CE_{timestamp}.pdf',
            'content': base64.b64encode(ce_buffer.getvalue()).decode('utf-8'),
            'type': 'application/pdf'
        }
        
        print("✅ Tous les fichiers générés")
        return files
        
    except Exception as e:
        print(f"❌ Erreur génération fichiers: {str(e)}")
        raise

def generate_graph_buffer(results):
    """Génère le graphique en mémoire (identique à extraction.py)"""
    if not results['mesures_montee']:
        return None
    
    try:
        pressions = [m['reference'] for m in results['mesures_montee']]
        erreurs_montee = [m['erreur'] for m in results['mesures_montee']]
        erreurs_descente = [m['erreur'] for m in results['mesures_descente']]
        
        plt.figure(figsize=(12, 8))
        
        # Graphique principal
        plt.subplot(2, 1, 1)
        plt.plot(pressions, erreurs_montee, 'bo-', label='Erreur montée', linewidth=2, markersize=6)
        if erreurs_descente:
            plt.plot(pressions[:len(erreurs_descente)], erreurs_descente, 'ro-', label='Erreur descente', linewidth=2, markersize=6)
        
        # Lignes de tolérance
        tolerance = results['tolerance']
        plt.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        plt.axhline(y=tolerance, color='r', linestyle='--', alpha=0.7, label=f'Tolérance ±{tolerance} {results["instrument_unite"]}')
        plt.axhline(y=-tolerance, color='r', linestyle='--', alpha=0.7)
        
        plt.xlabel(f'Pression ({results["instrument_unite"]})')
        plt.ylabel(f'Erreur ({results["instrument_unite"]})')
        plt.title(f'Courbe d\'étalonnage - {results["instrument_type"]}')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Graphique d'hystérésis
        if erreurs_descente and len(erreurs_descente) == len(erreurs_montee):
            plt.subplot(2, 1, 2)
            hysteresis = [abs(erreurs_montee[i] - erreurs_descente[i]) for i in range(len(erreurs_montee))]
            plt.plot(pressions, hysteresis, 'go-', label='Hystérésis', linewidth=2, markersize=6)
            plt.axhline(y=0, color='k', linestyle='--', alpha=0.5)
            plt.xlabel(f'Pression ({results["instrument_unite"]})')
            plt.ylabel(f'Hystérésis ({results["instrument_unite"]})')
            plt.title('Hystérésis en fonction de la pression')
            plt.grid(True, alpha=0.3)
            plt.legend()
        
        plt.tight_layout()
        
        # Sauvegarder en mémoire
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        plt.close()
        buffer.seek(0)
        
        return buffer
        
    except Exception as e:
        print(f"❌ Erreur génération graphique: {str(e)}")
        return None

def generate_cv_buffer(results):
    """Génère le CV en mémoire (identique à extraction.py)"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=2*cm, bottomMargin=2*cm)
    story = []
    
    # En-tête
    story.append(Paragraph("LABORATOIRE DE MÉTROLOGIE", styles['TitrePrincipal']))
    story.append(Paragraph("CHAÎNE D'ÉTALONNAGE - PRESSION", styles['SousTitre']))
    story.append(Spacer(1, 0.5*cm))
    
    # Numéro de constat
    story.append(Paragraph(f"<b>CONSTAT DE VÉRIFICATION N°: {results['numero_cv']}</b>", styles['Section']))
    story.append(Spacer(1, 0.3*cm))
    
    # Informations client
    story.append(Paragraph("DÉLIVRÉ À", styles['Section']))
    story.append(Paragraph(f"<b>Client :</b> {results['client_nom']}", styles['NormalEspace']))
    story.append(Paragraph(f"<b>Adresse :</b> {results['client_adresse']}", styles['NormalEspace']))
    story.append(Spacer(1, 0.3*cm))
    
    # Identification instrument
    story.append(Paragraph("IDENTIFICATION DE L'INSTRUMENT", styles['Section']))
    story.append(Paragraph(f"<b>Désignation :</b> {results['instrument_type']}", styles['NormalEspace']))
    story.append(Paragraph(f"<b>Constructeur :</b> {results['instrument_constructeur']}", styles['NormalEspace']))
    story.append(Paragraph(f"<b>N° de série :</b> {results['instrument_serie']}", styles['NormalEspace']))
    story.append(Paragraph(f"<b>N° d'identification :</b> {results['instrument_inventaire']}", styles['NormalEspace']))
    story.append(Paragraph(f"<b>Plage de mesure :</b> {results['instrument_pmin']} à {results['instrument_pmax']} {results['instrument_unite']}", styles['NormalEspace']))
    story.append(Spacer(1, 0.3*cm))
    
    # Conditions de vérification
    story.append(Paragraph("CONDITIONS DE VÉRIFICATION", styles['Section']))
    story.append(Paragraph(f"<b>Température :</b> {results['temperature']:.1f} °C", styles['NormalEspace']))
    story.append(Paragraph(f"<b>Humidité relative :</b> {results['humidite']:.1f} % HR", styles['NormalEspace']))
    story.append(Paragraph(f"<b>Pression atmosphérique :</b> {results['pression_atm']:.0f} hPa", styles['NormalEspace']))
    story.append(Paragraph(f"<b>Date de vérification :</b> {results['date_etalonnage']}", styles['NormalEspace']))
    story.append(Spacer(1, 0.3*cm))
    
    # Résultats
    story.append(Paragraph("RÉSULTATS DE VÉRIFICATION", styles['Section']))
    story.append(Paragraph(f"<b>Erreur de justesse maximale :</b> {results['erreur_max']:.3f} {results['instrument_unite']}", styles['NormalEspace']))
    story.append(Paragraph(f"<b>Erreur d'hystérésis maximale :</b> {results['hysteresis_max']:.3f} {results['instrument_unite']}", styles['NormalEspace']))
    story.append(Paragraph(f"<b>Tolérance :</b> ± {results['tolerance']} {results['instrument_unite']}", styles['NormalEspace']))
    story.append(Spacer(1, 0.3*cm))
    
    # Jugement
    jugement = f"Instrument {'CONFORME' if results['conforme'] else 'NON-CONFORME'} aux tolérances"
    story.append(Paragraph("JUGEMENT", styles['Section']))
    story.append(Paragraph(f"<b>{jugement}</b>", styles['NormalEspace']))
    story.append(Spacer(1, 0.5*cm))
    
    # Signature
    story.append(Paragraph(f"<b>Date d'émission :</b> {results['date_etalonnage']}", styles['NormalEspace']))
    story.append(Spacer(1, 0.5*cm))
    story.append(Paragraph("Responsable technique : _____________________", styles['NormalEspace']))
    story.append(Paragraph("Signature : _____________________", styles['NormalEspace']))
    
    doc.build(story)
    buffer.seek(0)
    return buffer

def generate_ce_buffer(results, graph_buffer):
    """Génère le CE en mémoire (identique à extraction.py)"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=2*cm, bottomMargin=2*cm)
    story = []
    
    # En-tête
    story.append(Paragraph("LABORATOIRE DE MÉTROLOGIE", styles['TitrePrincipal']))
    story.append(Paragraph("CP 20220 Casablanca, Maroc", styles['SousTitre']))
    story.append(Spacer(1, 0.5*cm))
    
    # Numéro de certificat
    story.append(Paragraph(f"<b>CERTIFICAT D'ÉTALONNAGE N° : {results['numero_ce']}</b>", styles['Section']))
    story.append(Spacer(1, 0.3*cm))
    
    # Contenu identique au CE de extraction.py...
    # (Ajout des autres sections comme dans extraction.py)
    
    # Ajouter le graphique si disponible
    if graph_buffer:
        story.append(Paragraph("COURBE D'ÉTALONNAGE", styles['Section']))
        # Sauvegarder temporairement le graphique
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_graph:
            temp_graph.write(graph_buffer.getvalue())
            temp_graph_path = temp_graph.name
        
        try:
            img = Image(temp_graph_path, width=15*cm, height=10*cm)
            story.append(img)
        finally:
            if os.path.exists(temp_graph_path):
                os.unlink(temp_graph_path)
    
    doc.build(story)
    buffer.seek(0)
    return buffer

if __name__ == '__main__':
    print("🚀 Démarrage du serveur API d'extraction...")
    print("📡 Endpoints disponibles:")
    print("   - GET  /health")
    print("   - POST /generate-certificates")
    print("🌐 Serveur démarré sur http://localhost:5000")
    
    app.run(host='0.0.0.0', port=8080, debug=True)
